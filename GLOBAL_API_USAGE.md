# Playwright 全局API回放功能使用指南

## 概述

通过patch-script的方式，我们将官方的Playwright回放API暴露为全局变量，实现了更直接、更可靠的回放功能。

## 核心优势

1. **完全官方兼容**：直接使用官方的`performAction`函数，无需修正API调用
2. **功能完整**：支持所有官方动作类型，包括断言动作
3. **无需callMetadata处理**：官方函数内部正确处理callMetadata参数
4. **易于维护**：跟随官方更新，减少维护成本

## 暴露的全局API

### `global.playwrightReplayAPI`

```javascript
{
  // 核心回放函数
  performAction: Function,
  
  // RecorderCollection类
  RecorderCollection: Class,
  
  // 工具函数
  mainFrameForAction: Function,
  buildFullSelector: Function,
  serverSideCallMetadata: Function,
  
  // 便捷方法
  createRecorderCollection: Function,
  executeAction: Function,
  getContext: Function,
  
  // 版本信息
  version: String,
  patchVersion: String
}
```

## 使用方法

### 1. 应用Patch

确保已应用包含`PLAYWRIGHT_REPLAY_API_PATCH`的patch：

```bash
npm run patch:apply
```

### 2. 启动Playwright录制器

全局API需要在Playwright录制器环境中才能使用：

```bash
# 方法1：启动录制器
npx playwright codegen

# 方法2：在你的应用中初始化Playwright上下文
```

### 3. 使用GlobalApiExecutor

```javascript
const { GlobalApiExecutor } = require('./src/main/global-api-executor');

const executor = new GlobalApiExecutor();

// 初始化
await executor.initialize({
  browserName: 'chromium',
  launchOptions: { headless: false }
});

// 执行动作
await executor.executeAction({
  name: 'navigate',
  url: 'https://www.baidu.com'
});

await executor.executeAction({
  name: 'fill',
  selector: '#kw',
  text: 'test'
});

await executor.executeAction({
  name: 'click',
  selector: '#su'
});
```

### 4. 直接使用全局API

```javascript
// 检查API可用性
if (global.playwrightReplayAPI) {
  console.log('API版本:', global.playwrightReplayAPI.version);
  
  // 创建RecorderCollection
  const collection = global.playwrightReplayAPI.createRecorderCollection(pageAliases);
  
  // 执行动作
  await global.playwrightReplayAPI.executeAction(pageAliases, actionInContext);
}
```

## 支持的动作类型

### 基础动作
- `navigate` - 页面导航
- `click` - 点击元素
- `fill` - 填写输入框
- `press` - 按键操作
- `check` - 选中复选框
- `uncheck` - 取消选中复选框
- `select` - 选择下拉选项
- `setInputFiles` - 上传文件

### 断言动作
- `assertChecked` - 断言复选框状态
- `assertText` - 断言文本内容
- `assertValue` - 断言输入值
- `assertVisible` - 断言元素可见性

## 测试

运行测试脚本验证功能：

```bash
node src/test/test-global-api-executor.js
```

## 故障排除

### 1. 全局API不可用

**症状**：`global.playwrightReplayAPI` 为 `undefined`

**解决方案**：
1. 确保已应用最新的patch
2. 启动Playwright录制器或相关服务
3. 检查patch是否正确应用

### 2. API调用失败

**症状**：执行动作时出错

**解决方案**：
1. 检查pageAliases映射是否正确
2. 确保页面已正确初始化
3. 验证动作数据格式

### 3. 版本兼容性问题

**症状**：patch应用失败或API不匹配

**解决方案**：
1. 检查Playwright版本是否匹配patch
2. 重新生成patch文件
3. 更新API调用方式

## 与现有方案对比

| 特性 | FixedOfficialExecutor | GlobalApiExecutor |
|------|----------------------|-------------------|
| API兼容性 | 需要修正callMetadata | 完全兼容官方API |
| 功能完整性 | 部分动作类型 | 所有官方动作类型 |
| 维护成本 | 需要跟踪API变化 | 自动跟随官方更新 |
| 可靠性 | 中等 | 高 |
| 实现复杂度 | 中等 | 低 |

## 最佳实践

1. **优先使用GlobalApiExecutor**：新项目建议使用基于全局API的执行器
2. **保留现有方案**：作为fallback方案，以防全局API不可用
3. **定期更新patch**：跟随Playwright版本更新patch文件
4. **充分测试**：在生产环境使用前进行充分测试

## 总结

通过patch-script暴露官方回放API是一个优雅的解决方案，它：
- 避免了API兼容性问题
- 提供了完整的官方功能
- 简化了维护工作
- 提高了可靠性

这种方式是对现有fixed-official-executor的重要补充和改进。
