name: "PR: bump driver Node.js"
on:
  workflow_dispatch:
  schedule:
    # At 10:00am UTC  (3AM PST) every tuesday and thursday to roll to new Node.js driver
    - cron: "0 10 * * 2,4"
jobs:
  trigger-nodejs-roll:
    name: Trigger Roll
    runs-on: ubuntu-22.04
    if: github.repository == 'microsoft/playwright'
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: node utils/build/update-playwright-driver-version.mjs
      - name: Prepare branch
        id: prepare-branch
        run: |
          if [[ "$(git status --porcelain)" == "" ]]; then
              echo "there are no changes";
              exit 0;
          fi
          echo "HAS_CHANGES=1" >> $GITHUB_OUTPUT
          BRANCH_NAME="roll-driver-nodejs/$(date +%Y-%b-%d)"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_OUTPUT
          git config --global user.name microsoft-playwright-automation[bot]
          git config --global user.email 203992400+microsoft-playwright-automation[bot]@users.noreply.github.com
          git checkout -b "$BRANCH_NAME"
          git add .
          git commit -m "chore(driver): roll driver to recent Node.js LTS version"
          git push origin $BRANCH_NAME
      - uses: actions/create-github-app-token@v2
        id: app-token
        with:
          app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
          private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
      - name: Create Pull Request
        if: ${{ steps.prepare-branch.outputs.HAS_CHANGES == '1' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ steps.app-token.outputs.token }}
          script: |
            await github.rest.pulls.create({
              owner: 'microsoft',
              repo: 'playwright',
              head: 'microsoft:${{ steps.prepare-branch.outputs.BRANCH_NAME }}',
              base: 'main',
              title: 'chore(driver): roll driver to recent Node.js LTS version',
            });
