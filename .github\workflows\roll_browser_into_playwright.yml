name: Roll Browser into Playwright

on:
  repository_dispatch:
    types: [roll_into_pw]

env:
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1
  BROWSER: ${{ github.event.client_payload.browser }}
  REVISION: ${{ github.event.client_payload.revision }}

permissions:
  contents: write

concurrency: 
  group: 'roll-browser-into-playwright-${{ github.event.client_payload.browser }}-${{ github.event.client_payload.revision }}'

jobs:
  roll:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
    - run: npm run build
    - name: Install dependencies
      run: npx playwright install-deps
    - name: Roll to new revision
      run: |
        ./utils/roll_browser.js $BROWSER $REVISION
        npm run build
    - name: Prepare branch
      id: prepare-branch
      run: |
        BRANCH_NAME="roll-into-pw-${BROWSER}/${REVISION}"
        echo "BRANCH_NAME=$<PERSON><PERSON>CH_NAME" >> $GITHUB_OUTPUT

        git fetch origin $BRANCH_NAME:$BRANCH_NAME || true
        if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
          echo "exists=1" >> $GITHUB_OUTPUT
          echo "branch $BRANCH_NAME already exists, exiting"
          exit 0
        fi
        echo "exists=0" >> $GITHUB_OUTPUT

        git config --global user.name microsoft-playwright-automation[bot]
        git config --global user.email 203992400+microsoft-playwright-automation[bot]@users.noreply.github.com
        git checkout -b "$BRANCH_NAME"
        git add .
        git commit -m "feat(${BROWSER}): roll to r${REVISION}"
        git push origin $BRANCH_NAME --force
    - uses: actions/create-github-app-token@v2
      id: app-token
      with:
        app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
        private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
    - name: Create Pull Request
      uses: actions/github-script@v7
      if: ${{ steps.prepare-branch.outputs.exists == '0' }}
      with:
        github-token: ${{ steps.app-token.outputs.token }}
        script: |
          const response = await github.rest.pulls.create({
            owner: 'microsoft',
            repo: 'playwright',
            head: 'microsoft:${{ steps.prepare-branch.outputs.BRANCH_NAME }}',
            base: 'main',
            title: 'feat(${{ env.BROWSER }}): roll to r${{ env.REVISION }}',
          });
          await github.rest.issues.addLabels({
            owner: 'microsoft',
            repo: 'playwright',
            issue_number: response.data.number,
            labels: ['CQ1'],
          });
