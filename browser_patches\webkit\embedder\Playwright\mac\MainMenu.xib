<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="15505" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="15505"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="NSApplication">
            <connections>
                <outlet property="delegate" destination="494" id="495"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <menu title="AMainMenu" systemMenu="main" id="29">
            <items>
                <menuItem title="Playwright" id="56">
                    <menu key="submenu" title="Playwright" systemMenu="apple" id="57">
                        <items>
                            <menuItem title="About Playwright" id="58">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="orderFrontStandardAboutPanel:" target="-2" id="142"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="236">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Preferences…" keyEquivalent="," id="129"/>
                            <menuItem isSeparatorItem="YES" id="143">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Services" id="131">
                                <menu key="submenu" title="Services" systemMenu="services" id="130"/>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="144">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Hide Playwright" keyEquivalent="h" id="134">
                                <connections>
                                    <action selector="hide:" target="-1" id="367"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Hide Others" keyEquivalent="h" id="145">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="hideOtherApplications:" target="-1" id="368"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Show All" id="150">
                                <connections>
                                    <action selector="unhideAllApplications:" target="-1" id="370"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="149">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Quit Playwright" keyEquivalent="q" id="136">
                                <connections>
                                    <action selector="terminate:" target="-3" id="449"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="File" id="83">
                    <menu key="submenu" title="File" id="81">
                        <items>
                            <menuItem title="Open Location " tag="1" keyEquivalent="l" id="82">
                                <connections>
                                    <action selector="openLocation:" target="-1" id="575"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="79">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Close" keyEquivalent="w" id="73">
                                <connections>
                                    <action selector="performClose:" target="-1" id="193"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save" keyEquivalent="s" id="75">
                                <connections>
                                    <action selector="saveDocument:" target="-1" id="362"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save As…" keyEquivalent="S" id="80">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="saveDocumentAs:" target="-1" id="363"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save As PDF…" keyEquivalent="S" id="gmS-3Q-oLs">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="saveAsPDF:" target="-1" id="25T-Id-334"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save As WebArchive..." id="112">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="saveAsWebArchive:" target="-1" id="AGx-3e-6Nt"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="74">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Page Setup..." keyEquivalent="P" id="77">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="runPageLayout:" target="-1" id="87"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Print…" keyEquivalent="p" id="78">
                                <connections>
                                    <action selector="printWebView:" target="-1" id="86"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Edit" id="217">
                    <menu key="submenu" title="Edit" id="205">
                        <items>
                            <menuItem title="Undo" keyEquivalent="z" id="207">
                                <connections>
                                    <action selector="undo:" target="-1" id="223"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Redo" keyEquivalent="Z" id="215">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="redo:" target="-1" id="231"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="206">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Cut" keyEquivalent="x" id="199">
                                <connections>
                                    <action selector="cut:" target="-1" id="228"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy" keyEquivalent="c" id="197">
                                <connections>
                                    <action selector="copy:" target="-1" id="224"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Paste" keyEquivalent="v" id="203">
                                <connections>
                                    <action selector="paste:" target="-1" id="226"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Delete" id="202">
                                <connections>
                                    <action selector="delete:" target="-1" id="235"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Select All" keyEquivalent="a" id="198">
                                <connections>
                                    <action selector="selectAll:" target="-1" id="232"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="214">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Find" id="218">
                                <menu key="submenu" title="Find" id="220">
                                    <items>
                                        <menuItem title="Find…" tag="1" keyEquivalent="f" id="209">
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="241"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Next" tag="2" keyEquivalent="g" id="208">
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="487"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Previous" tag="3" keyEquivalent="G" id="213">
                                            <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="488"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Use Selection for Find" tag="7" keyEquivalent="e" id="221">
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="489"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Jump to Selection" keyEquivalent="j" id="210">
                                            <connections>
                                                <action selector="centerSelectionInVisibleArea:" target="-1" id="245"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="View" id="295">
                    <menu key="submenu" title="View" id="296">
                        <items>
                            <menuItem title="Zoom In" keyEquivalent="+" id="555">
                                <connections>
                                    <action selector="zoomIn:" target="-1" id="559"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Zoom Out" keyEquivalent="-" id="557">
                                <connections>
                                    <action selector="zoomOut:" target="-1" id="560"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Reset Zoom" keyEquivalent="0" id="558">
                                <connections>
                                    <action selector="resetZoom:" target="-1" id="561"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Zoom Text Only" id="562">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="toggleZoomMode:" target="-1" id="564"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Page Scale" id="Hzb-c3-Qfv">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Page Scale" id="jdo-5V-3CM">
                                    <items>
                                        <menuItem title="100%" state="on" tag="1" keyEquivalent="1" id="wHb-mR-Fv0">
                                            <connections>
                                                <action selector="setPageScale:" target="-1" id="uMw-eY-289"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="125%" tag="2" keyEquivalent="2" id="u4i-F7-rPb">
                                            <connections>
                                                <action selector="setPageScale:" target="-1" id="IbE-Ep-hfc"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="150%" tag="3" keyEquivalent="3" id="fwT-Iy-oK9">
                                            <connections>
                                                <action selector="setPageScale:" target="-1" id="9c0-eN-0f5"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="200%" tag="4" keyEquivalent="4" id="R5e-ct-O2u">
                                            <connections>
                                                <action selector="setPageScale:" target="-1" id="mkk-gh-dlN"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="View Scale" id="8UY-Pj-H13">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="View Scale" id="Ml8-mk-ffu">
                                    <items>
                                        <menuItem title="100%" state="on" tag="1" id="EAm-Xn-VrC">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="setViewScale:" target="-1" id="dkW-CI-RG4"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="75%" tag="2" id="jcA-I1-Cbq">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="setViewScale:" target="-1" id="BAF-Ym-IqV"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="50%" tag="3" id="BRM-D5-YNO">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="setViewScale:" target="-1" id="nXy-2b-9Zz"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="25%" tag="4" id="gzk-YK-x0W">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="setViewScale:" target="-1" id="KXK-f6-24N"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Reload Page" keyEquivalent="r" id="579">
                                <connections>
                                    <action selector="reload:" target="-1" id="582"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Debug" id="534">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <menu key="submenu" title="Debug" id="535">
                        <items>
                            <menuItem title="Show Web Inspector" keyEquivalent="i" id="xso-9z-R4u">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="showHideWebInspector:" target="-1" id="Cmx-g7-EPb"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Window" id="19">
                    <menu key="submenu" title="Window" systemMenu="window" id="24">
                        <items>
                            <menuItem title="Minimize" keyEquivalent="m" id="23">
                                <connections>
                                    <action selector="performMiniaturize:" target="-1" id="37"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Zoom" id="239">
                                <connections>
                                    <action selector="performZoom:" target="-1" id="240"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="92">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Bring All to Front" id="5">
                                <connections>
                                    <action selector="arrangeInFront:" target="-1" id="39"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
            </items>
            <point key="canvasLocation" x="139" y="-50"/>
        </menu>
        <customObject id="494" customClass="BrowserAppDelegate"/>
    </objects>
</document>
