/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

.test-result {
  flex: auto;
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
}

.test-result > div {
  flex: none;
}

.test-result video,
.test-result img.screenshot {
  flex: none;
  box-shadow: var(--box-shadow-thick);
  margin: 24px auto;
  min-width: 200px;
  max-width: 80%;
}

.test-result-path {
  padding: 0 0 0 5px;
  color: var(--color-fg-muted);
}

.test-result-counter {
  border-radius: 12px;
  color: var(--color-canvas-default);
  padding: 2px 8px;
}

@media(prefers-color-scheme: light) {
  .test-result-counter {
    background: var(--color-scale-gray-5);
  }
}

@media(prefers-color-scheme: dark) {
  .test-result-counter {
    background: var(--color-scale-gray-3);
  }
}

@media only screen and (max-width: 600px) {
  .test-result {
    padding: 0 !important;
  }
}
