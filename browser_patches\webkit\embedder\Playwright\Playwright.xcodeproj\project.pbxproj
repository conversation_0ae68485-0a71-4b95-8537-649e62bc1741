// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		256AC3DA0F4B6AC300CF336A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 256AC3D90F4B6AC300CF336A /* AppDelegate.m */; };
		51E244FA11EFCE07008228D2 /* MBToolbarItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 51E244F911EFCE07008228D2 /* MBToolbarItem.m */; };
		5C9332AF24C1349C0036DECF /* SecurityInterface.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5C9332AE24C1349C0036DECF /* SecurityInterface.framework */; };
		7A8E843E26858D80008EC0B1 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7A8E843D26858D80008EC0B1 /* Images.xcassets */; };
		BC329487116A92E2008635D1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = BC329486116A92E2008635D1 /* main.m */; };
		BC329498116A941B008635D1 /* BrowserWindowController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC329497116A941B008635D1 /* BrowserWindowController.m */; };
		BC72B89511E57E07001EB4EB /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = 1DDD58150DA1D0A300B3202A /* MainMenu.xib */; };
		BC72B89611E57E0F001EB4EB /* BrowserWindow.xib in Resources */ = {isa = PBXBuildFile; fileRef = BC3294A2116A9852008635D1 /* BrowserWindow.xib */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1AFFEF761860EE6800DA465F /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		1AFFEF781860EE6800DA465F /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		1DDD58150DA1D0A300B3202A /* MainMenu.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = MainMenu.xib; path = mac/MainMenu.xib; sourceTree = "<group>"; };
		256AC3D80F4B6AC300CF336A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = mac/AppDelegate.h; sourceTree = "<group>"; };
		256AC3D90F4B6AC300CF336A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = mac/AppDelegate.m; sourceTree = "<group>"; };
		256AC3F00F4B6AF500CF336A /* Playwright_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Playwright_Prefix.pch; path = mac/Playwright_Prefix.pch; sourceTree = "<group>"; };
		29B97324FDCFA39411CA2CEB /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		29B97325FDCFA39411CA2CEB /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		37BAF90620218053000EA87A /* Playwright.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Playwright.entitlements; sourceTree = "<group>"; };
		51E244F811EFCE07008228D2 /* MBToolbarItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MBToolbarItem.h; sourceTree = "<group>"; };
		51E244F911EFCE07008228D2 /* MBToolbarItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MBToolbarItem.m; sourceTree = "<group>"; };
		5C9332AE24C1349C0036DECF /* SecurityInterface.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SecurityInterface.framework; path = System/Library/Frameworks/SecurityInterface.framework; sourceTree = SDKROOT; };
		7A8E843D26858D80008EC0B1 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		8D1107320486CEB800E47091 /* Playwright.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Playwright.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1B89B95221E027A00EB4CEB /* SDKVariant.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = SDKVariant.xcconfig; sourceTree = "<group>"; };
		BC329486116A92E2008635D1 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = mac/main.m; sourceTree = "<group>"; };
		BC329496116A941B008635D1 /* BrowserWindowController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BrowserWindowController.h; path = mac/BrowserWindowController.h; sourceTree = "<group>"; };
		BC329497116A941B008635D1 /* BrowserWindowController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = BrowserWindowController.m; path = mac/BrowserWindowController.m; sourceTree = "<group>"; };
		BC3294A2116A9852008635D1 /* BrowserWindow.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = BrowserWindow.xib; path = mac/BrowserWindow.xib; sourceTree = "<group>"; };
		BC72B89A11E57E8A001EB4EB /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = mac/Info.plist; sourceTree = "<group>"; };
		BCA8CBDD11E578A000812FB8 /* Base.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = Base.xcconfig; sourceTree = "<group>"; };
		BCA8CBDE11E578A000812FB8 /* DebugRelease.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = DebugRelease.xcconfig; sourceTree = "<group>"; };
		BCA8CBDF11E578A000812FB8 /* Playwright.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = Playwright.xcconfig; sourceTree = "<group>"; };
		F393B1A6286A71AE007B8F61 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = WebKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8D11072E0486CEB800E47091 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5C9332AF24C1349C0036DECF /* SecurityInterface.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		080E96DDFE201D6D7F000002 /* Playwright */ = {
			isa = PBXGroup;
			children = (
				256AC3D80F4B6AC300CF336A /* AppDelegate.h */,
				256AC3D90F4B6AC300CF336A /* AppDelegate.m */,
				BC329496116A941B008635D1 /* BrowserWindowController.h */,
				BC329497116A941B008635D1 /* BrowserWindowController.m */,
				7A8E843D26858D80008EC0B1 /* Images.xcassets */,
				BC72B89A11E57E8A001EB4EB /* Info.plist */,
				BC329486116A92E2008635D1 /* main.m */,
				51E244F811EFCE07008228D2 /* MBToolbarItem.h */,
				51E244F911EFCE07008228D2 /* MBToolbarItem.m */,
				37BAF90620218053000EA87A /* Playwright.entitlements */,
			);
			name = Playwright;
			sourceTree = "<group>";
		};
		1058C7A2FEA54F0111CA2CBC /* Other Frameworks */ = {
			isa = PBXGroup;
			children = (
				29B97324FDCFA39411CA2CEB /* AppKit.framework */,
				1AFFEF781860EE6800DA465F /* CoreData.framework */,
				29B97325FDCFA39411CA2CEB /* Foundation.framework */,
			);
			name = "Other Frameworks";
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBC /* Products */ = {
			isa = PBXGroup;
			children = (
				8D1107320486CEB800E47091 /* Playwright.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEB /* Playwright */ = {
			isa = PBXGroup;
			children = (
				256AC3F00F4B6AF500CF336A /* Playwright_Prefix.pch */,
				080E96DDFE201D6D7F000002 /* Playwright */,
				29B97317FDCFA39411CA2CEB /* Resources */,
				BCA8CBDA11E5787800812FB8 /* Configurations */,
				29B97323FDCFA39411CA2CEB /* Frameworks */,
				19C28FACFE9D520D11CA2CBC /* Products */,
			);
			name = Playwright;
			sourceTree = "<group>";
		};
		29B97317FDCFA39411CA2CEB /* Resources */ = {
			isa = PBXGroup;
			children = (
				BC3294A2116A9852008635D1 /* BrowserWindow.xib */,
				1DDD58150DA1D0A300B3202A /* MainMenu.xib */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1058C7A2FEA54F0111CA2CBC /* Other Frameworks */,
				1AFFEF761860EE6800DA465F /* Cocoa.framework */,
				5C9332AE24C1349C0036DECF /* SecurityInterface.framework */,
				F393B1A6286A71AE007B8F61 /* WebKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BCA8CBDA11E5787800812FB8 /* Configurations */ = {
			isa = PBXGroup;
			children = (
				BCA8CBDD11E578A000812FB8 /* Base.xcconfig */,
				BCA8CBDE11E578A000812FB8 /* DebugRelease.xcconfig */,
				BCA8CBDF11E578A000812FB8 /* Playwright.xcconfig */,
				A1B89B95221E027A00EB4CEB /* SDKVariant.xcconfig */,
			);
			path = Configurations;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8D1107260486CEB800E47091 /* Playwright */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C01FCF4A08A954540054247C /* Build configuration list for PBXNativeTarget "Playwright" */;
			buildPhases = (
				8D1107290486CEB800E47091 /* Resources */,
				8D11072C0486CEB800E47091 /* Sources */,
				8D11072E0486CEB800E47091 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Playwright;
			productInstallPath = "$(HOME)/Applications";
			productName = Playwright;
			productReference = 8D1107320486CEB800E47091 /* Playwright.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0700;
				LastUpgradeCheck = 1000;
				TargetAttributes = {
					8D1107260486CEB800E47091 = {
						SystemCapabilities = {
							com.apple.Sandbox = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Playwright" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
			);
			mainGroup = 29B97314FDCFA39411CA2CEB /* Playwright */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8D1107260486CEB800E47091 /* Playwright */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8D1107290486CEB800E47091 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BC72B89611E57E0F001EB4EB /* BrowserWindow.xib in Resources */,
				7A8E843E26858D80008EC0B1 /* Images.xcassets in Resources */,
				BC72B89511E57E07001EB4EB /* MainMenu.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8D11072C0486CEB800E47091 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				256AC3DA0F4B6AC300CF336A /* AppDelegate.m in Sources */,
				BC329498116A941B008635D1 /* BrowserWindowController.m in Sources */,
				BC329487116A92E2008635D1 /* main.m in Sources */,
				51E244FA11EFCE07008228D2 /* MBToolbarItem.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		C01FCF4B08A954540054247C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCA8CBDF11E578A000812FB8 /* Playwright.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
			};
			name = Debug;
		};
		C01FCF4C08A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCA8CBDF11E578A000812FB8 /* Playwright.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
			};
			name = Release;
		};
		C01FCF4F08A954540054247C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCA8CBDE11E578A000812FB8 /* DebugRelease.xcconfig */;
			buildSettings = {
				GCC_OPTIMIZATION_LEVEL = 0;
			};
			name = Debug;
		};
		C01FCF5008A954540054247C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCA8CBDE11E578A000812FB8 /* DebugRelease.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C01FCF4A08A954540054247C /* Build configuration list for PBXNativeTarget "Playwright" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4B08A954540054247C /* Debug */,
				C01FCF4C08A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Playwright" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247C /* Debug */,
				C01FCF5008A954540054247C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEC /* Project object */;
}
