name: "publish release - <PERSON><PERSON>ie<PERSON>"

on:
  release:
    types: [published]

jobs:
  publish-trace-viewer:
    name: "publish Trace Viewer to trace.playwright.dev"
    runs-on: ubuntu-24.04
    if: github.repository == 'microsoft/playwright'
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - uses: actions/create-github-app-token@v2
      id: app-token
      with:
        app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
        private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
        repositories: trace.playwright.dev
    - name: Deploy Stable
      run: bash utils/build/deploy-trace-viewer.sh --stable
      env:
        GH_SERVICE_ACCOUNT_TOKEN: ${{ steps.app-token.outputs.token }}
