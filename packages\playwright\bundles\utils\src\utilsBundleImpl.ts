/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable import/order */

import json5Library from 'json5';
export const json5 = json5Library;

import sourceMapSupportLibrary from 'source-map-support';
export const sourceMapSupport = sourceMapSupportLibrary;

import stoppableLibrary from 'stoppable';
export const stoppable = stoppableLibrary;

import enquirerLibrary from 'enquirer';
export const enquirer = enquirerLibrary;

import chokidarLibrary from 'chokidar';
export const chokidar = chokidarLibrary;

import * as getEastAsianWidthLibrary from 'get-east-asian-width';
export const getEastAsianWidth = getEastAsianWidthLibrary;
