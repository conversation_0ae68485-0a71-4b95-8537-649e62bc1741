/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

.header-view-status-container {
  float: right;
}

.header-view {
  padding: 12px 8px 0 8px;
}

.header-view div {
  flex-shrink: 0;
}

.header-superheader {
  color: var(--color-fg-muted);
}

.header-title {
  flex: none;
  font-weight: 400;
  font-size: 32px;
  line-height: 1.25;
}

@media only screen and (max-width: 600px) {
  .header-view {
    padding: 0;
  }

  .header-view div {
    /* Allow all header elements to wrap */
    flex-shrink: 1;
  }

  .header-view-status-container {
    float: none;
    margin: 0 0 10px 0 !important;
    overflow: hidden;
  }

  .header-view-status-container .subnav-search-input {
    border-left: none;
    border-right: none;
  }

  .header-title, .header-superheader {
    margin: 0 8px;
  }
}
