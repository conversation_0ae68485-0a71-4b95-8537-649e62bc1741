---
id: languages
title: "Supported languages"
---

## Introduction

Playwright is available in multiple languages that share the same underlying implementation. All core features for automating the browser are supported in all languages, while testing ecosystem integration is different. Pick the language based on your experience, familiarity with its testing ecosystem and your project constraints. For the best experience pick the test runner that we recommend for each language.

## JavaScript and TypeScript

Playwright for Node.js comes with its own [test runner](https://playwright.dev/docs/running-tests) that provides great parallelization mechanism, screenshot assertions, html reporter, automatic tracing etc.

* [Documentation](https://playwright.dev/docs/intro)
* [GitHub repo](https://github.com/microsoft/playwright)

## Python

Playwright [Pytest plugin](https://playwright.dev/python/docs/test-runners) is the recommended way to run end-to-end tests. It provides context isolation, running it on multiple browser configurations and more out of the box.

* [Documentation](https://playwright.dev/python/docs/intro)
* [GitHub repo](https://github.com/microsoft/playwright-python)

## Java

You can choose any testing framework such as JUnit or TestNG based on your project requirements.

* [Documentation](https://playwright.dev/java/docs/intro)
* [GitHub repo](https://github.com/microsoft/playwright-java)

## .NET

Playwright for .NET comes with MSTest, NUnit, and xUnit [base classes](https://playwright.dev/dotnet/docs/test-runners) for writing end-to-end tests.

* [Documentation](https://playwright.dev/dotnet/docs/intro)
* [GitHub repo](https://github.com/microsoft/playwright-dotnet)
