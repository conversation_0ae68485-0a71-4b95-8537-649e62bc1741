{"name": "babel-bundle", "version": "0.0.1", "private": true, "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/core": "^7.26.10", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.10", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-proposal-explicit-resource-management": "^7.25.9", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-import-attributes": "^7.26.0", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-class-static-block": "^7.26.0", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-logical-assignment-operators": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.26.6", "@babel/plugin-transform-numeric-separator": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/preset-typescript": "^7.26.0"}, "devDependencies": {"@types/babel__code-frame": "^7.0.6", "@types/babel__core": "^7.20.5", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__traverse": "^7.20.6"}}