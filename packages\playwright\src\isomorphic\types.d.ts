/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export type GitCommitInfo = {
  shortHash: string;
  hash: string;
  subject: string;
  body: string;
  author: {
    name: string;
    email: string;
    time: number;
  };
  committer: {
    name: string;
    email: string
    time: number;
  };
  branch: string;
};

export type CIInfo = {
  commitHref: string;
  prHref?: string;
  prTitle?: string;
  prBaseHash?: string;
  buildHref?: string;
  commitHash?: string;
  branch?: string;
};

export type MetadataWithCommitInfo = {
  ci?: CIInfo;
  gitCommit?: GitCommitInfo;
  gitDiff?: string;
};
