/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

:root {
  --box-shadow: rgba(0, 0, 0, 0.133) 0px 1.6px 3.6px 0px, rgba(0, 0, 0, 0.11) 0px 0.3px 0.9px 0px;
  --box-shadow-thick: rgb(0 0 0 / 10%) 0px 1.8px 1.9px,
    rgb(0 0 0 / 15%) 0px 6.1px 6.3px,
    rgb(0 0 0 / 10%) 0px -2px 4px,
    rgb(0 0 0 / 15%) 0px -6.1px 12px,
    rgb(0 0 0 / 25%) 0px 6px 12px;
}

* {
  box-sizing: border-box;
  min-width: 0;
  min-height: 0;
}

svg {
  fill: currentColor;
}

.vbox {
  display: flex;
  flex-direction: column;
  flex: auto;
  position: relative;
}

.hbox {
  display: flex;
  flex: auto;
  position: relative;
}

.hidden {
  visibility: hidden;
}

.d-flex {
  display: flex !important;
}

.d-inline {
  display: inline !important;
}

.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 16px; }
.m-4 { margin: 24px; }
.m-5 { margin: 32px; }

.mx-1 { margin: 0 4px; }
.mx-2 { margin: 0 8px; }
.mx-3 { margin: 0 16px; }
.mx-4 { margin: 0 24px; }
.mx-5 { margin: 0 32px; }

.my-1 { margin: 4px 0; }
.my-2 { margin: 8px 0; }
.my-3 { margin: 16px 0; }
.my-4 { margin: 24px 0; }
.my-5 { margin: 32px 0; }

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 16px; }
.mt-4 { margin-top: 24px; }
.mt-5 { margin-top: 32px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 16px; }
.mr-4 { margin-right: 24px; }
.mr-5 { margin-right: 32px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 16px; }
.ml-4 { margin-left: 24px; }
.ml-5 { margin-left: 32px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 16px; }
.p-4 { padding: 24px; }
.p-5 { padding: 32px; }

.px-1 { padding: 0 4px; }
.px-2 { padding: 0 8px; }
.px-3 { padding: 0 16px; }
.px-4 { padding: 0 24px; }
.px-5 { padding: 0 32px; }

.py-1 { padding: 4px 0; }
.py-2 { padding: 8px 0; }
.py-3 { padding: 16px 0; }
.py-4 { padding: 24px 0; }
.py-5 { padding: 32px 0; }

.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 16px; }
.pt-4 { padding-top: 24px; }
.pt-5 { padding-top: 32px; }

.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 16px; }
.pr-4 { padding-right: 24px; }
.pr-5 { padding-right: 32px; }

.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 16px; }
.pb-4 { padding-bottom: 24px; }
.pb-5 { padding-bottom: 32px; }

.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 16px; }
.pl-4 { padding-left: 24px; }
.pl-5 { padding-left: 32px; }

.no-wrap {
  white-space: nowrap !important;
}

.float-left {
  float: left !important;
}

article, aside, details, figcaption, figure, footer, header, main, menu, nav, section {
  display: block;
}

.form-control, .form-select {
  padding: 5px 12px;
  font-size: 14px;
  line-height: 20px;
  color: var(--color-fg-default);
  vertical-align: middle;
  background-color: var(--color-canvas-default);
  background-repeat: no-repeat;
  background-position: right 8px center;
  border: 1px solid var(--color-border-default);
  border-radius: 6px;
  outline: none;
  box-shadow: var(--color-primer-shadow-inset);
}

.input-contrast {
  background-color: var(--color-canvas-inset);
}

.subnav-search {
  position: relative;
  flex: auto;
  display: flex;
}

.subnav-search-input {
  flex: auto;
  padding-left: 32px;
  color: var(--color-fg-muted);
}

.subnav-search-icon {
  position: absolute;
  top: 9px;
  left: 8px;
  display: block;
  color: var(--color-fg-muted);
  text-align: center;
  pointer-events: none;
}

.subnav-search-context + .subnav-search {
  margin-left: -1px;
}

.subnav-item {
  flex: none;
  position: relative;
  float: left;
  padding: 5px 10px;
  font-weight: 500;
  line-height: 20px;
  color: var(--color-fg-default);
  border: 1px solid var(--color-border-default);
}

.subnav-item:hover {
  background-color: var(--color-canvas-subtle);
}

.subnav-item:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.subnav-item:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.subnav-item + .subnav-item {
  margin-left: -1px;
}

.counter {
  display: inline-block;
  min-width: 20px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  color: var(--color-fg-default);
  text-align: center;
  background-color: var(--color-neutral-muted);
  border: 1px solid transparent;
  border-radius: 2em;
}

.color-icon-success {
  color: var(--color-success-fg) !important;
}

.color-text-danger {
  color: var(--color-danger-fg) !important;
}

.color-text-warning {
  color: var(--color-checks-step-warning-text) !important;
}

.color-fg-muted {
  color: var(--color-fg-muted) !important;
}

.octicon {
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
  margin-right: 7px;
  flex: none;
}

.button {
  flex: none;
  height: 24px;
  border: 1px solid var(--color-btn-border);
  outline: none;
  color: var(--color-btn-text);
  background: var(--color-btn-bg);
  padding: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.button:not(:disabled):hover {
  border-color: var(--color-btn-hover-border);
  background-color: var(--color-btn-hover-bg);
}

@media only screen and (max-width: 600px) {
  .subnav-item, .form-control {
    border-radius: 0 !important;
  }

  .subnav-item {
    padding: 5px 3px;
    border: none;
  }

  .subnav-search-input {
    border-left: 0;
    border-right: 0;
  }
}
