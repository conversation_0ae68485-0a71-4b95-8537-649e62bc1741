name: "publish release - Docker"

on:
  workflow_dispatch:
  release:
    types: [published]

env:
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  publish-docker-release:
    name: "publish to DockerHub"
    runs-on: ubuntu-22.04
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    if: github.repository == 'microsoft/playwright'
    environment: allow-publishing-docker-to-acr
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        registry-url: 'https://registry.npmjs.org'
    - name: Set up Docker QEMU for arm64 docker builds
      uses: docker/setup-qemu-action@v3
      with:
        platforms: arm64
    - run: npm ci
    - run: npm run build
    - name: Azure Login
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.AZURE_DOCKER_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_DOCKER_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_DOCKER_SUBSCRIPTION_ID }}
    - name: <PERSON>gin to ACR via OIDC
      run: az acr login --name playwright
    - run: ./utils/docker/publish_docker.sh stable
