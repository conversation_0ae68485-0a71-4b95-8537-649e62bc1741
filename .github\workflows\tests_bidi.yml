name: tests BiDi

on:
  workflow_dispatch:
    inputs:
      ref:
        description: Playwright SHA / ref to test. Use 'refs/pull/PULL_REQUEST_ID/head' to test a PR. Defaults to the current branch.
        required: false
        default: ''
  pull_request:
    branches:
      - main
    paths:
      - .github/workflows/tests_bidi.yml
      - packages/playwright-core/src/server/bidi/**
      - tests/bidi/**
  schedule:
    # Run every day at midnight
    - cron: '0 0 * * *'

env:
  FORCE_COLOR: 1

jobs:
  test_bidi:
    name: BiDi
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ubuntu-24.04
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    strategy:
      fail-fast: false
      matrix:
        channel: [bidi-chromium, moz-firefox]
    steps:
    - uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
    - uses: actions/checkout@v4
      if: github.event_name == 'workflow_dispatch'
      with:
        ref: ${{ github.event.inputs.ref }}
    - uses: actions/setup-node@v4
      with:
        node-version: 20
    - run: npm ci
    - run: npm run build
    - run: npx playwright install --with-deps chromium
      if: matrix.channel == 'bidi-chromium'
    - if: matrix.channel == 'moz-firefox'
      id: install_firefox
      run: |
        npx -y @puppeteer/browsers install firefox@nightly |\
          awk 'END { $1=""; sub(/^ /,""); print "bidi_ffpath="$0 }' |\
          tee -a $GITHUB_OUTPUT
    - name: Run tests
      run: xvfb-run --auto-servernum --server-args="-screen 0 1280x960x24" -- npm run biditest -- --project=${{ matrix.channel }}*
      env:
        PWTEST_USE_BIDI_EXPECTATIONS: '1'
        BIDI_FFPATH: ${{ steps.install_firefox.outputs.bidi_ffpath }}
    - name: Upload csv report to GitHub
      if: ${{ !cancelled() }}
      uses: actions/upload-artifact@v4
      with:
        name: csv-report-${{ matrix.channel }}
        path: test-results/report.csv
        retention-days: 7

    - name: Azure Login
      if: ${{ !cancelled() && github.ref == 'refs/heads/main' }}
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.AZURE_BLOB_REPORTS_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_BLOB_REPORTS_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_BLOB_REPORTS_SUBSCRIPTION_ID }}

    - name: Upload report.csv to Azure
      if: ${{ !cancelled() && github.ref == 'refs/heads/main' }}
      run: |
        REPORT_DIR='bidi-reports'
        azcopy cp "./test-results/report.csv" "https://mspwblobreport.blob.core.windows.net/\$web/$REPORT_DIR/${{ matrix.channel }}.csv"
        echo "Report url: https://mspwblobreport.z1.web.core.windows.net/$REPORT_DIR/${{ matrix.channel }}.csv"
      env:
        AZCOPY_AUTO_LOGIN_TYPE: AZCLI
