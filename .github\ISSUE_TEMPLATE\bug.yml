name: Bug Report 🪲
description: Create a bug report to help us improve
title: '[Bug]: '
body:
  - type: markdown
    attributes:
      value: |
        # Please follow these steps first:
  - type: markdown
    attributes:
      value: |
        ## Troubleshoot
        If <PERSON><PERSON> is not behaving the way you expect, we'd ask you to look at the [documentation](https://playwright.dev/docs/intro) and search the issue tracker for evidence supporting your expectation.
        Please make reasonable efforts to troubleshoot and rule out issues with your code, the configuration, or any 3rd party libraries you might be using.
        Playwright offers [several debugging tools](https://playwright.dev/docs/debug) that you can use to troubleshoot your issues.
  - type: markdown
    attributes:
      value: |
        ## Ask for help through appropriate channels
        If you feel unsure about the cause of the problem, consider asking for help on for example [StackOverflow](https://stackoverflow.com/questions/ask) or our [Discord channel](https://aka.ms/playwright/discord) before posting a bug report. The issue tracker is not a help forum.
  - type: markdown
    attributes:
      value: |
        ## Make a minimal reproduction
        To file the report, you will need a GitHub repository with a minimal (but complete) example and simple/clear steps on how to reproduce the bug.
        The simpler you can make it, the more likely we are to successfully verify and fix the bug. You can create a new project with `npm init playwright@latest new-project` and then add the test code there.
        Please make sure you only include the code and the dependencies absolutely necessary for your repro. Due to the security considerations, we can only run the code we trust. Major web frameworks are Ok to use, but smaller convenience libraries are not. 
  - type: markdown
    attributes:
      value: |
        > [!IMPORTANT]
        > Bug reports without a minimal reproduction will be rejected.

        ---
  - type: input
    id: version
    attributes:
      label: Version
      description: |
        The version of Playwright you are using.
        Is it the [latest](https://github.com/microsoft/playwright/releases)? Test and see if the bug has already been fixed.
      placeholder: ex. 1.41.1
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Steps to reproduce
      description: Please link to a repository with a minimal reproduction and describe accurately how we can reproduce/verify the bug.
      placeholder: |
        Example steps (replace with your own):
        1. Clone my repo at https://github.com/<myuser>/example
        2. npm install
        3. npm run test
        4. You should see the error come up
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: A description of what you expect to happen.
      placeholder: I expect to see X or Y
    validations:
      required: true
  - type: textarea
    id: what-happened
    attributes:
      label: Actual behavior
      description: |
        A clear and concise description of the unexpected behavior.
        Please include any relevant output here, especially any error messages.
      placeholder: A bug happened!
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Anything else that might be relevant
    validations:
      required: false
  - type: textarea
    id: envinfo
    attributes:
      label: Environment
      description: |
        Please paste the output of running `npx envinfo --preset playwright`.
        This will be automatically formatted as a code block, so no need for backticks.
      placeholder: |
        System:
          OS: Linux 6.2 Ubuntu 22.04.3 LTS 22.04.3 LTS (Jammy Jellyfish)
          CPU: (8) arm64
        Binaries:
          Node: 18.19.0 - ~/.nvm/versions/node/v18.19.0/bin/node
          npm: 10.2.3 - ~/.nvm/versions/node/v18.19.0/bin/npm
        npmPackages:
          @playwright/test: 1.41.1 => 1.41.1
      render: shell
    validations:
      required: true
