/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

:host {
  font-size: 13px;
  font-family: system-ui, "Ubuntu", "Droid Sans", sans-serif;
  color: #333;
}

svg {
  position: absolute;
  height: 0;
}

x-pw-tooltip {
  backdrop-filter: blur(5px);
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 0.5rem 1.2rem rgba(0,0,0,.3);
  display: none;
  font-size: 12.8px;
  font-weight: normal;
  left: 0;
  line-height: 1.5;
  max-width: 600px;
  position: absolute;
  top: 0;
  padding: 0;
  flex-direction: column;
  overflow: hidden;
}

x-pw-tooltip-line {
  display: flex;
  max-width: 600px;
  padding: 6px;
  user-select: none;
  cursor: pointer;
}

x-pw-tooltip-line.selectable:hover {
  background-color: hsl(0, 0%, 95%);
  overflow: hidden;
}

x-pw-tooltip-footer {
  display: flex;
  max-width: 600px;
  padding: 6px;
  user-select: none;
  color: #777;
}

x-pw-dialog {
  background-color: white;
  pointer-events: auto;
  border-radius: 6px;
  box-shadow: 0 0.5rem 1.2rem rgba(0,0,0,.3);
  display: flex;
  flex-direction: column;
  position: absolute;
  width: 400px;
  height: 150px;
  z-index: 10;
  font-size: 13px;
}

x-pw-dialog-body {
  display: flex;
  flex-direction: column;
  flex: auto;
}

x-pw-dialog-body label {
  margin: 5px 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

x-pw-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

x-pw-action-point {
  position: absolute;
  width: 20px;
  height: 20px;
  background: red;
  border-radius: 10px;
  margin: -10px 0 0 -10px;
  z-index: 2;
}

x-pw-separator {
  height: 1px;
  margin: 6px 9px;
  background: rgb(148 148 148 / 90%);
}

x-pw-tool-gripper {
  height: 28px;
  width: 24px;
  margin: 2px 0;
  cursor: grab;
}

x-pw-tool-gripper:active {
  cursor: grabbing;
}

x-pw-tool-gripper > x-div {
  width: 16px;
  height: 16px;
  margin: 6px 4px;
  clip-path: url(#icon-gripper);
  background-color: #555555;
}

x-pw-tools-list > label {
  display: flex;
  align-items: center;
  margin: 0 10px;
  user-select: none;
}

x-pw-tools-list {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #dddddd;
}

x-pw-tool-item {
  pointer-events: auto;
  height: 28px;
  width: 28px;
  border-radius: 3px;
}

x-pw-tool-item:not(.disabled) {
  cursor: pointer;
}

x-pw-tool-item:not(.disabled):hover {
  background-color: hsl(0, 0%, 86%);
}

x-pw-tool-item.toggled {
  background-color: rgba(138, 202, 228, 0.5);
}

x-pw-tool-item.toggled:not(.disabled):hover {
  background-color: #8acae4c4;
}

x-pw-tool-item > x-div {
  width: 16px;
  height: 16px;
  margin: 6px;
  background-color: #3a3a3a;
}

x-pw-tool-item.disabled > x-div {
  background-color: rgba(97, 97, 97, 0.5);
  cursor: default;
}

x-pw-tool-item.record.toggled {
  background-color: transparent;
}

x-pw-tool-item.record.toggled:not(.disabled):hover {
  background-color: hsl(0, 0%, 86%);
}

x-pw-tool-item.record.toggled > x-div {
  background-color: #a1260d;
}

x-pw-tool-item.record.disabled.toggled > x-div {
  opacity: 0.8;
}

x-pw-tool-item.accept > x-div {
  background-color: #388a34;
}

x-pw-tool-item.record > x-div {
  /* codicon: circle-large-filled */
  clip-path: url(#icon-circle-large-filled);
}

x-pw-tool-item.pick-locator > x-div {
  /* codicon: inspect */
  clip-path: url(#icon-inspect);
}

x-pw-tool-item.text > x-div {
  /* codicon: whole-word */
  clip-path: url(#icon-whole-word);
}

x-pw-tool-item.visibility > x-div {
  /* codicon: eye */
  clip-path: url(#icon-eye);
}

x-pw-tool-item.value > x-div {
  /* codicon: symbol-constant */
  clip-path: url(#icon-symbol-constant);
}

x-pw-tool-item.snapshot > x-div {
  /* codicon: eye */
  clip-path: url(#icon-gist);
}

x-pw-tool-item.accept > x-div {
  clip-path: url(#icon-check);
}

x-pw-tool-item.cancel > x-div {
  clip-path: url(#icon-close);
}

x-pw-tool-item.succeeded > x-div {
  /* codicon: pass */
  clip-path: url(#icon-pass);
  background-color: #388a34 !important;
}

x-pw-overlay {
  position: absolute;
  top: 0;
  max-width: min-content;
  z-index: 2147483647;
  background: transparent;
  pointer-events: auto;
}

x-pw-overlay x-pw-tools-list {
  background-color: #ffffffdd;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 5px;
  border-radius: 3px;
  border-bottom: none;
}

x-pw-overlay x-pw-tool-item {
  margin: 2px;
}

textarea.text-editor {
  font-family: system-ui,Ubuntu,Droid Sans,sans-serif;
  flex: auto;
  border: none;
  margin: 6px 10px;
  color: #333;
  outline: 1px solid transparent!important;
  resize: none;
  padding: 0;
  font-size: 13px;
}

textarea.text-editor.does-not-match {
  outline: 1px solid red !important;
}

x-div {
  display: block;
}

x-spacer {
  flex: auto;
}

* {
  box-sizing: border-box;
}

*[hidden] {
  display: none !important;
}

x-locator-editor {
  flex: none;
  width: 100%;
  height: 60px;
  padding: 4px;
  border-bottom: 1px solid #dddddd;
  outline: 1px solid transparent;
}

x-locator-editor.does-not-match {
  outline: 1px solid red;
}

.CodeMirror {
  width: 100% !important;
  height: 100% !important;
}
