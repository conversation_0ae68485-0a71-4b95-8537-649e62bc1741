/**
 * 基于全局API的官方回放执行器
 * 
 * 使用通过patch暴露的官方Playwright回放API
 * 这种方式直接使用官方的performAction函数，无需修正API调用
 */

const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');
const { EventEmitter } = require('events');

/**
 * 基于全局API的官方执行器
 */
class GlobalApiExecutor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      timeout: 15000,
      enableDiagnostics: true,
      ...options
    };
    
    this.browser = null;
    this.context = null;
    this.pages = new Map();
    this.pageAliases = new Map();
    this.recorderCollection = null;
    this.pageCounter = 0;
  }

  async initialize(config) {
    try {
      console.log('🚀 初始化基于全局API的官方执行器...');
      
      // 检查全局API是否可用
      if (!global.playwrightReplayAPI) {
        throw new Error('全局 Playwright 回放API未找到。请确保已应用patch并启动了Playwright录制器。');
      }
      
      console.log('✅ 发现全局 Playwright 回放API:', global.playwrightReplayAPI.version);
      console.log('📋 可用方法:', Object.keys(global.playwrightReplayAPI));
      
      const browserType = {
        'chromium': chromium,
        'firefox': firefox,
        'webkit': webkit
      }[config.browserName] || chromium;
      
      this.browser = await browserType.launch({
        headless: false,
        ...config.launchOptions
      });
      
      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        ...config.contextOptions
      });
      
      this._setupPageListeners();
      
      const page = await this.context.newPage();
      await this._registerPage(page, 'page');
      
      // 使用全局API创建RecorderCollection
      this.recorderCollection = global.playwrightReplayAPI.createRecorderCollection(this.pageAliases);
      this.recorderCollection.setEnabled(true);
      
      console.log('✅ 基于全局API的官方执行器初始化完成');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  _setupPageListeners() {
    this.context.on('page', async (page) => {
      const pageAlias = this._generatePageAlias();
      await this._registerPage(page, pageAlias);
      console.log(`📄 新页面创建: ${pageAlias}`);
    });
  }

  async _registerPage(page, pageAlias) {
    this.pages.set(pageAlias, page);
    this.pageAliases.set(page, pageAlias);
    
    page.on('close', () => {
      console.log(`📄 页面关闭: ${pageAlias}`);
      this.pages.delete(pageAlias);
      this.pageAliases.delete(page);
    });
  }

  _generatePageAlias() {
    return this.pageCounter === 0 ? 'page' : `page${++this.pageCounter}`;
  }

  async executeAction(actionData) {
    try {
      if (!global.playwrightReplayAPI) {
        throw new Error('全局 Playwright 回放API不可用');
      }

      const actionInContext = {
        frame: {
          pageAlias: actionData.pageAlias || 'page',
          framePath: actionData.framePath || [],
          isMainFrame: actionData.isMainFrame !== false
        },
        action: {
          name: actionData.name,
          selector: actionData.selector,
          signals: actionData.signals || [],
          ...this._extractActionData(actionData)
        },
        startTime: Date.now()
      };

      if (this.options.enableDiagnostics) {
        console.log(`🎬 开始执行: ${actionInContext.action.name}${actionInContext.action.selector ? ` -> ${actionInContext.action.selector}` : ''}`);
      }

      // 使用全局API的executeAction方法
      await global.playwrightReplayAPI.executeAction(this.pageAliases, actionInContext);
      
      if (this.options.enableDiagnostics) {
        const duration = Date.now() - actionInContext.startTime;
        console.log(`✅ 执行完成: ${actionInContext.action.name} (耗时: ${duration}ms)`);
      }
      
    } catch (error) {
      console.error(`❌ 执行动作失败: ${actionData.name} - ${error.message}`);
      throw error;
    }
  }

  _extractActionData(actionData) {
    const result = {};
    
    switch (actionData.name) {
      case 'navigate':
        result.url = actionData.url || '';
        break;
      case 'click':
        result.button = actionData.button || 'left';
        result.modifiers = actionData.modifiers || 0;
        result.clickCount = actionData.clickCount || 1;
        if (actionData.position) result.position = actionData.position;
        break;
      case 'fill':
        result.text = actionData.text || '';
        break;
      case 'press':
        result.key = actionData.key || '';
        result.modifiers = actionData.modifiers || 0;
        break;
      case 'select':
        result.options = actionData.options || [];
        break;
      case 'assertChecked':
        result.checked = actionData.checked;
        break;
      case 'assertText':
        result.text = actionData.text || '';
        break;
      case 'assertValue':
        result.value = actionData.value || '';
        break;
    }
    
    return result;
  }

  async executeJsonFile(jsonFile) {
    const lines = fs.readFileSync(jsonFile, 'utf-8').split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 文件为空');
    }
    
    const config = JSON.parse(lines[0]);
    await this.initialize(config);
    
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  getCurrentPage() {
    return this.pages.get('page');
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.pages.clear();
      this.pageAliases.clear();
    }
  }

  // 获取全局API信息
  getGlobalApiInfo() {
    if (!global.playwrightReplayAPI) {
      return { available: false, message: '全局API不可用' };
    }
    
    return {
      available: true,
      version: global.playwrightReplayAPI.version,
      patchVersion: global.playwrightReplayAPI.patchVersion,
      methods: Object.keys(global.playwrightReplayAPI)
    };
  }
}

module.exports = { 
  GlobalApiExecutor
};
