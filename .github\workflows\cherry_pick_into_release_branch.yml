name: Cherry-pick into release branch

on:
  workflow_dispatch:
    inputs:
      version:
        type: string
        description: Version number, e.g. 1.25
        required: true
      commit_hashes:
        type: string
        description: Comma-separated list of commit hashes to cherry-pick
        required: true

permissions:
  contents: write

jobs:
  roll:
    runs-on: ubuntu-22.04
    steps:
    - name: Validate input version number
      run: |
        VERSION="${{ github.event.inputs.version }}"
        if ! [[ "$VERSION" =~ ^[0-9]+\.[0-9]+$ ]]; then
          echo "Version is not a two digit semver version"
          exit 1
        fi
    - uses: actions/checkout@v4
      with:
        ref: release-${{ github.event.inputs.version }}
        fetch-depth: 0
    - name: Cherry-pick commits
      id: cherry-pick
      run: |
        git config --global user.name microsoft-playwright-automation[bot]
        git config --global user.email 203992400+microsoft-playwright-automation[bot]@users.noreply.github.com
        for COMMIT_HASH in $(echo "${{ github.event.inputs.commit_hashes }}" | tr "," "\n"); do
          git cherry-pick --no-commit "$COMMIT_HASH"

          COMMIT_MESSAGE="$(git show -s --format=%B $COMMIT_HASH | head -n 1)"
          COMMIT_MESSAGE=$(node -e '
            const match = /^(.*) (\(#\d+\))$/.exec(process.argv[1]);
            if (!match) {
              console.log(process.argv[1]);
              process.exit(0);
            }
            console.log(`cherry-pick${match[2]}: ${match[1]}`);
          ' "$COMMIT_MESSAGE")

          git commit -m "$COMMIT_MESSAGE"
        done
        LAST_COMMIT_MESSAGE=$(git show -s --format=%B)
        echo "PR_TITLE=$LAST_COMMIT_MESSAGE" >> $GITHUB_OUTPUT
    - name: Prepare branch
      id: prepare-branch
      run: |
        BRANCH_NAME="cherry-pick-${{ github.event.inputs.version }}-$(date +%Y-%m-%d-%H-%M-%S)"
        echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_OUTPUT
        git checkout -b "$BRANCH_NAME"
        git push origin $BRANCH_NAME
    - uses: actions/create-github-app-token@v2
      id: app-token
      with:
        app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
        private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
    - name: Create Pull Request
      uses: actions/github-script@v7
      with:
        github-token: ${{ steps.app-token.outputs.token }}
        script: |
          const readableCommitHashesList = '${{ github.event.inputs.commit_hashes }}'.split(',').map(hash => `- ${hash}`).join('\n');
          const response = await github.rest.pulls.create({
            owner: 'microsoft',
            repo: 'playwright',
            head: 'microsoft:${{ steps.prepare-branch.outputs.BRANCH_NAME }}',
            base: 'release-${{ github.event.inputs.version }}',
            title: '${{ steps.cherry-pick.outputs.PR_TITLE }}',
            body: `This PR cherry-picks the following commits:\n\n${readableCommitHashesList}`,
          });
          await github.rest.issues.addLabels({
            owner: 'microsoft',
            repo: 'playwright',
            issue_number: response.data.number,
            labels: ['CQ1'],
          });
