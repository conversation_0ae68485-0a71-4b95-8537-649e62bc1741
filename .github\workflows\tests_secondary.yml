name: "tests 2"

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    paths-ignore:
      - 'browser_patches/**'
      - 'docs/**'
    types: [ labeled ]
    branches:
      - main
      - release-*

env:
  # Force terminal colors. @see https://www.npmjs.com/package/colors
  FORCE_COLOR: 1
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

permissions:
  id-token: write   # This is required for OIDC login (azure/login) to succeed
  contents: read    # This is required for actions/checkout to succeed

jobs:
  test_linux:
    name: ${{ matrix.os }} (${{ matrix.browser }})
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        os: [ubuntu-24.04]
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-*
        bot-name: "${{ matrix.browser }}-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  test_mac:
    name: ${{ matrix.os }} (${{ matrix.browser }})
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        # Intel: *-large
        # Arm64: *-xlarge
        os: [macos-13-large, macos-13-xlarge, macos-14-large, macos-14-xlarge]
        browser: [chromium, firefox, webkit]
        include:
          - os: macos-15-large
            browser: webkit
          - os: macos-15-xlarge
            browser: webkit
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-*
        bot-name: "${{ matrix.browser }}-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  test_win:
    name: "Windows"
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-* ${{ matrix.browser == 'firefox' && '--workers 1' || '' }}
        bot-name: "${{ matrix.browser }}-windows-latest"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  test-package-installations-other-node-versions:
    name: "Installation Test ${{ matrix.os }} (${{ matrix.node_version }})"
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ${{ matrix.os  }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - os: ubuntu-latest
          node_version: 20
        - os: ubuntu-latest
          node_version: 22
    timeout-minutes: 30
    steps:
    - uses: actions/checkout@v4
    - run: npm install -g yarn@1
    - run: npm install -g pnpm@8
    - name: Setup Ubuntu Binary Installation # TODO: Remove when https://github.com/electron/electron/issues/42510 is fixed
      if: ${{ runner.os == 'Linux' }}
      run: |
        if grep -q "Ubuntu 24" /etc/os-release; then
          sudo sysctl -w kernel.apparmor_restrict_unprivileged_userns=0
        fi
      shell: bash
    - uses: ./.github/actions/run-test
      with:
        node-version: ${{ matrix.node_version }}
        command: npm run itest
        bot-name: "package-installations-${{ matrix.os }}-node${{ matrix.node_version }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  headed_tests:
    name: "headed ${{ matrix.browser }} (${{ matrix.os }})"
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        os: [ubuntu-24.04, macos-14-xlarge, windows-latest]
        include:
          # We have different binaries per Ubuntu version for WebKit.
          - browser: webkit
            os: ubuntu-22.04
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-* --headed
        bot-name: "${{ matrix.browser }}-headed-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}

  transport_linux:
    name: "Transport"
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        mode: [driver, service]
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: chromium
        command: npm run ctest
        bot-name: "${{ matrix.mode }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_MODE: ${{ matrix.mode }}

  tracing_linux:
    name: Tracing ${{ matrix.browser }} ${{ matrix.channel }}
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - browser: chromium
          - browser: firefox
          - browser: webkit
          - browser: chromium
            channel: chromium-tip-of-tree
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium ${{ matrix.channel }}
        command: npm run test -- --project=${{ matrix.browser }}-*
        bot-name: "tracing-${{ matrix.channel || matrix.browser }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_TRACE: 1
        PWTEST_CHANNEL: ${{ matrix.channel }}

  test_chromium_channels:
    name: Test ${{ matrix.channel }} on ${{ matrix.runs-on }}
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ${{ matrix.runs-on }}
    strategy:
      fail-fast: false
      matrix:
        channel: [chrome, chrome-beta, msedge, msedge-beta, msedge-dev]
        runs-on: [ubuntu-22.04, macos-latest, windows-latest]
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.channel }}
        command: npm run ctest
        bot-name: ${{ matrix.channel }}-${{ matrix.runs-on }}
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: ${{ matrix.channel }}

  chromium_tot:
    name: Chromium tip-of-tree ${{ matrix.os }}${{ matrix.headed }}
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ${{ matrix.os  }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04, macos-13, windows-latest]
        headed: ['--headed', '']
        exclude:
          # Tested in tests_primary.yml already
          - os: ubuntu-22.04
            headed: ''
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: chromium-tip-of-tree
        command: npm run ctest -- ${{ matrix.headed }}
        bot-name: "chromium-tip-of-tree-${{ matrix.os }}${{ matrix.headed }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: chromium-tip-of-tree

  chromium_tot_headless_shell:
    name: Chromium tip-of-tree headless-shell-${{ matrix.os }}
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ${{ matrix.os  }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: chromium-tip-of-tree-headless-shell
        command: npm run ctest
        bot-name: "chromium-tip-of-tree-headless-shell-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: chromium-tip-of-tree-headless-shell

  firefox_beta:
    name: Firefox Beta ${{ matrix.os }}
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    runs-on: ${{ matrix.os  }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04, windows-latest, macos-latest]
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: firefox-beta chromium
        command: npm run ftest
        bot-name: "firefox-beta-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: firefox-beta

  build-playwright-driver:
    name: "build-playwright-driver"
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
    - run: npm run build
    - run: utils/build/build-playwright-driver.sh

  test_channel_chromium:
    name: Test channel=chromium
    environment: ${{ github.event_name == 'push' && 'allow-uploading-flakiness-results' || null }}
    strategy:
      fail-fast: false
      matrix:
        runs-on: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.runs-on }}
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        # TODO: this should pass --no-shell.
        # However, codegen tests do not inherit the channel and try to launch headless shell.
        browsers-to-install: chromium
        command: npm run ctest
        bot-name: "channel-chromium-${{ matrix.runs-on }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_CHANNEL: chromium
