# class: Coverage
* since: v1.11
* langs: js

Coverage gathers information about parts of JavaScript and CSS that were used by the page.

An example of using JavaScript coverage to produce Istanbul report for page load:

:::note
Coverage APIs are only supported on Chromium-based browsers.
:::

```js
const { chromium } = require('playwright');
const v8toIstanbul = require('v8-to-istanbul');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.coverage.startJSCoverage();
  await page.goto('https://chromium.org');
  const coverage = await page.coverage.stopJSCoverage();
  for (const entry of coverage) {
    const converter = v8toIstanbul('', 0, { source: entry.source });
    await converter.load();
    converter.applyCoverage(entry.functions);
    console.log(JSON.stringify(converter.toIstanbul()));
  }
  await browser.close();
})();
```

## async method: Coverage.startCSSCoverage
* since: v1.11

Returns coverage is started

### option: Coverage.startCSSCoverage.resetOnNavigation
* since: v1.11
- `resetOnNavigation` <[boolean]>

Whether to reset coverage on every navigation. Defaults to `true`.

## async method: Coverage.startJSCoverage
* since: v1.11

Returns coverage is started

:::note
Anonymous scripts are ones that don't have an associated url. These are scripts that are dynamically created
on the page using `eval` or `new Function`. If [`option: reportAnonymousScripts`] is set to `true`, anonymous scripts
will have `__playwright_evaluation_script__` as their URL.
:::

### option: Coverage.startJSCoverage.resetOnNavigation
* since: v1.11
- `resetOnNavigation` <[boolean]>

Whether to reset coverage on every navigation. Defaults to `true`.

### option: Coverage.startJSCoverage.reportAnonymousScripts
* since: v1.11
- `reportAnonymousScripts` <[boolean]>

Whether anonymous scripts generated by the page should be reported. Defaults to `false`.

## async method: Coverage.stopCSSCoverage
* since: v1.11
- returns: <[Array]<[Object]>>
  - `url` <[string]> StyleSheet URL
  - `text` ?<[string]> StyleSheet content, if available.
  - `ranges` <[Array]<[Object]>> StyleSheet ranges that were used. Ranges are sorted and non-overlapping.
    - `start` <[int]> A start offset in text, inclusive
    - `end` <[int]> An end offset in text, exclusive

Returns the array of coverage reports for all stylesheets

:::note
CSS Coverage doesn't include dynamically injected style tags without sourceURLs.
:::

## async method: Coverage.stopJSCoverage
* since: v1.11
- returns: <[Array]<[Object]>>
  - `url` <[string]> Script URL
  - `scriptId` <[string]> Script ID
  - `source` ?<[string]> Script content, if applicable.
  - `functions` <[Array]<[Object]>> V8-specific coverage format.
    - `functionName` <[string]>
    - `isBlockCoverage` <[boolean]>
    - `ranges` <[Array]<[Object]>>
      - `count` <[int]>
      - `startOffset` <[int]>
      - `endOffset` <[int]>

Returns the array of coverage reports for all scripts

:::note
JavaScript Coverage doesn't include anonymous scripts by default. However, scripts with sourceURLs are
reported.
:::
