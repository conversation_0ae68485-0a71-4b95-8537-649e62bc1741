# -*- Mode: python; indent-tabs-mode: nil; tab-width: 40 -*-
# vim: set filetype=python:
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

Classes = [
    {
        'cid': '{d8c4d9e0-9462-445e-9e43-68d3872ad1de}',
        'contract_ids': ['@mozilla.org/juggler/screencast;1'],
        'type': 'nsIScreencastService',
        'constructor': 'mozilla::nsScreencastService::GetSingleton',
        'headers': ['/juggler/screencast/nsScreencastService.h'],
    },
]
