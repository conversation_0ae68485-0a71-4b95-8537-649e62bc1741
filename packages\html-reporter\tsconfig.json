{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "useUnknownInCatchVariables": false,
    "paths": {
      "@protocol/*": ["../protocol/src/*"],
      "@web/*": ["../web/src/*"],
      "@playwright/*": ["../playwright/src/*"],
      "@recorder/*": ["../recorder/src/*"],
      "@testIsomorphic/*": ["../playwright/src/isomorphic/*"],
      "playwright-core/lib/*": ["../playwright-core/src/*"],
      "playwright/lib/*": ["../playwright/src/*"],
    }
  },
  "include": ["src", "../web/src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
