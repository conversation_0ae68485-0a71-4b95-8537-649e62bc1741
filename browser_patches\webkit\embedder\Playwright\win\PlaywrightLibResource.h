//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by PlaywrightLib.rc
//
#define IDC_MYICON                      2
#define IDD_PLAYWRIGHT_DIALOG           102
#define IDS_APP_TITLE                   103
#define IDD_ABOUTBOX                    103
#define IDM_ABOUT                       104
#define IDI_PLAYWRIGHT                  107
#define IDC_PLAYWRIGHT                  109
#define IDM_WEB_INSPECTOR               120
#define IDM_INVERT_COLORS               125
#define IDR_MAINFRAME                   128
#define IDD_CACHES                      129
#define IDM_HISTORY_BACKWARD            130
#define IDD_USER_AGENT                  130
#define IDM_HISTORY_FORWARD             131
#define IDM_HISTORY_LINK0               150
#define IDM_HISTORY_LINK1               151
#define IDM_HISTORY_LINK2               152
#define IDM_HISTORY_LINK3               153
#define IDM_HISTORY_LINK4               154
#define IDM_HISTORY_LINK5               155
#define IDM_HISTORY_LINK6               156
#define IDM_HISTORY_LINK7               157
#define IDM_HISTORY_LINK8               158
#define IDM_HISTORY_LINK9               159
#define IDT_UPDATE_STATS                160
#define IDM_ACTUAL_SIZE                 172
#define IDM_ZOOM_IN                     173
#define IDM_ZOOM_OUT                    174
#define IDD_AUTH                        176
#define IDD_PROXY                       178
#define IDD_SERVER_TRUST                179
#define IDR_ACCELERATORS_PRE            180
#define IDB_TOOLBAR                     181
#define IDC_EMPTY_URL_CACHE             1000
#define IDC_RETURN_FREE_MEMORY          1001
#define IDC_EMPTY_WEBCORE_CACHE         1002
#define IDC_CHECK1                      1003
#define IDC_HEAP_OBJECTS                1005
#define IDC_GC_JSC                      1006
#define IDC_RESERVED_VM                 1007
#define IDC_COMMITTED_VM                1008
#define IDC_FREE_LIST_BYTES             1009
#define IDC_IMAGES_OBJECT_COUNT         1011
#define IDC_CSS_OBJECT_COUNT            1012
#define IDC_XSL_OBJECT_COUNT            1013
#define IDC_JSC_OBJECT_COUNT            1014
#define IDC_TOTAL_OBJECT_COUNT          1015
#define IDC_IMAGES_BYTES                1016
#define IDC_CSS_BYTES                   1017
#define IDC_XSL_BYTES                   1018
#define IDC_JSC_BYTES                   1019
#define IDC_TOTAL_BYTES                 1020
#define IDC_IMAGES_LIVE_COUNT           1021
#define IDC_CSS_LIVE_COUNT              1022
#define IDC_XSL_LIVE_COUNT              1023
#define IDC_JSC_LIVE_COUNT              1024
#define IDC_TOTAL_LIVE_COUNT            1025
#define IDC_IMAGES_DECODED_COUNT        1026
#define IDC_CSS_DECODED_COUNT           1027
#define IDC_XSL_DECODED_COUNT           1028
#define IDC_JSC_DECODED_COUNT           1029
#define IDC_TOTAL_DECODED               1030
#define IDC_IMAGES_PURGEABLE_COUNT      1031
#define IDC_CSS_PURGEABLE_COUNT         1032
#define IDC_XSL_PURGEABLE_COUNT         1033
#define IDC_JSC_PURGEABLE_COUNT         1034
#define IDC_TOTAL_PURGEABLE             1035
#define IDC_TOTAL_JSC_HEAP_OBJECTS      1036
#define IDC_GLOBAL_JSC_HEAP_OBJECTS     1037
#define IDC_PROTECTED_JSC_HEAP_OBJECTS  1038
#define IDC_STATIC56                    1039
#define IDC_STATIC57                    1040
#define IDC_JSC_HEAP_SIZE               1041
#define IDC_JSC_HEAP_FREE               1042
#define IDC_BUTTON5                     1043
#define IDC_TOTAL_FONT_OBJECTS          1044
#define IDC_Message                     1044
#define IDC_INACTIVE_FONT_OBJECTS       1045
#define IDC_GLYPH_PAGES                 1046
#define IDC_PAGE_URL_MAPPINGS           1047
#define IDC_RETAINED_PAGE_URLS          1048
#define IDC_SITE_ICON_RECORDS           1049
#define IDC_TOTAL_FONT_OBJECTS5         1050
#define IDC_SITE_ICONS_WITH_DATA        1051
#define IDC_USER_AGENT_INPUT            1052
#define IDC_AUTH_USER                   1053
#define IDC_AUTH_PASSWORD               1054
#define IDC_URL_BAR                     1055
#define IDC_REALM_TEXT                  1056
#define IDC_PROXY_URL                   1057
#define IDC_PROXY_DEFAULT               1058
#define IDC_PROXY_CUSTOM                1059
#define IDC_PROXY_EXCLUDE               1060
#define IDC_PROXY_DISABLE               1061
#define IDC_SERVER_TRUST_TEXT           1062
#define IDM_NEW_WINDOW                  32776
#define IDM_RELOAD                      32779
#define IDM_CLOSE_WINDOW                32780
#define IDC_STATIC                      -1

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        182
#define _APS_NEXT_COMMAND_VALUE         32783
#define _APS_NEXT_CONTROL_VALUE         1063
#define _APS_NEXT_SYMED_VALUE           110
#endif
#endif
