<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16096" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16096"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="BrowserWindowController">
            <connections>
                <outlet property="backButton" destination="40" id="46"/>
                <outlet property="containerView" destination="9" id="37"/>
                <outlet property="forwardButton" destination="42" id="47"/>
                <outlet property="lockButton" destination="mWN-r5-XQb" id="49"/>
                <outlet property="progressIndicator" destination="21" id="33"/>
                <outlet property="reloadButton" destination="23" id="34"/>
                <outlet property="share" destination="1hB-AH-eUl" id="si4-8e-DsM"/>
                <outlet property="toggleUseShrinkToFitButton" destination="82" id="9w7-AB-Ye3"/>
                <outlet property="toolbar" destination="48" id="67"/>
                <outlet property="urlText" destination="10" id="32"/>
                <outlet property="window" destination="1" id="3"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" frameAutosaveName="Main Window" animationBehavior="default" id="1" customClass="CustomWindow">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" fullSizeContentView="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="517" y="330" width="776" height="608"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <view key="contentView" id="2">
                <rect key="frame" x="0.0" y="0.0" width="776" height="608"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                <subviews>
                    <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9">
                        <rect key="frame" x="0.0" y="0.0" width="776" height="608"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </customView>
                </subviews>
            </view>
            <toolbar key="toolbar" implicitIdentifier="994A0CB1-7575-4F39-A65B-7165AB1E8015" displayMode="iconOnly" sizeMode="regular" id="48">
                <allowedToolbarItems>
                    <toolbarItem implicitItemIdentifier="73DE9F4B-73E2-4036-A134-2D9E029DA980" label="Go Back" paletteLabel="Go Back" image="NSGoLeftTemplate" id="56" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="32" height="25"/>
                        <size key="maxSize" width="32" height="25"/>
                        <button key="view" verticalHuggingPriority="750" id="40">
                            <rect key="frame" x="10" y="14" width="32" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSGoLeftTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="41">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                        </button>
                        <connections>
                            <action selector="goBack:" target="-2" id="61"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="E1A9D32A-59E3-467B-9ABA-A95780416E69" label="Go Forward" paletteLabel="Go Forward" image="NSGoRightTemplate" id="57" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="32" height="25"/>
                        <size key="maxSize" width="32" height="27"/>
                        <button key="view" verticalHuggingPriority="750" id="42">
                            <rect key="frame" x="18" y="14" width="32" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSGoRightTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="43">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                        </button>
                        <connections>
                            <action selector="goForward:" target="-2" id="62"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="88C16109-D40F-4682-BCE4-CBEE2EDE32D2" label="Refresh" paletteLabel="Refresh" image="NSRefreshTemplate" id="58" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="29" height="25"/>
                        <size key="maxSize" width="29" height="27"/>
                        <button key="view" verticalHuggingPriority="750" id="23">
                            <rect key="frame" x="10" y="14" width="29" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSRefreshTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="24">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="reload:" target="-2" id="35"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="F9C3B2C4-B22D-4E12-92BC-EA326711BBC1" label="Lock" paletteLabel="Lock" image="NSLockUnlockedTemplate" id="Ky3-6Y-3U1" userLabel="Lock" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="29" height="25"/>
                        <size key="maxSize" width="29" height="27"/>
                        <button key="view" verticalHuggingPriority="750" id="mWN-r5-XQb">
                            <rect key="frame" x="2" y="14" width="29" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSLockUnlockedTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="iRv-ey-QZe">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="showCertificate:" target="-2" id="LjW-s0-oJ2"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="76DCF2B0-1DDE-47D2-9212-705E6E310CCE" label="Use Shrink To Fit" paletteLabel="Use Shrink To Fit" image="NSEnterFullScreenTemplate" id="81" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="29" height="25"/>
                        <size key="maxSize" width="29" height="27"/>
                        <button key="view" verticalHuggingPriority="750" id="82">
                            <rect key="frame" x="34" y="14" width="29" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSEnterFullScreenTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="83">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="toggleShrinkToFit:" target="-2" id="gp7-Vk-KTI"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="F1738B7F-895C-48F7-955D-0915E150BE1B" label="Share" paletteLabel="Share" image="NSShareTemplate" id="dJx-dw-gcC" customClass="MBToolbarItem">
                        <nil key="toolTip"/>
                        <size key="minSize" width="29" height="25"/>
                        <size key="maxSize" width="29" height="27"/>
                        <button key="view" verticalHuggingPriority="750" id="1hB-AH-eUl">
                            <rect key="frame" x="5" y="14" width="29" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="NSShareTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="S1v-UD-QhI">
                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                                <connections>
                                    <action selector="share:" target="-2" id="5e3-hg-bKN"/>
                                </connections>
                            </buttonCell>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="255D29F2-C9AA-4B4B-BB43-B38FCD6A0BBB" label="Location" paletteLabel="Location" id="59">
                        <nil key="toolTip"/>
                        <size key="minSize" width="50" height="22"/>
                        <size key="maxSize" width="100000" height="22"/>
                        <textField key="view" verticalHuggingPriority="750" id="10">
                            <rect key="frame" x="0.0" y="14" width="565" height="22"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="11">
                                <font key="font" metaFont="system"/>
                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                            </textFieldCell>
                            <connections>
                                <action selector="fetch:" target="-2" id="36"/>
                            </connections>
                        </textField>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="86912BAA-B8D0-400F-BFEE-71FC166986E6" label="Progress" paletteLabel="Progress" tag="-1" sizingBehavior="auto" id="60">
                        <nil key="toolTip"/>
                        <progressIndicator key="view" horizontalHuggingPriority="750" verticalHuggingPriority="750" maxValue="1" displayedWhenStopped="NO" bezeled="NO" controlSize="small" style="spinning" id="21">
                            <rect key="frame" x="19" y="14" width="16" height="16"/>
                            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        </progressIndicator>
                    </toolbarItem>
                </allowedToolbarItems>
                <defaultToolbarItems>
                    <toolbarItem reference="56"/>
                    <toolbarItem reference="57"/>
                    <toolbarItem reference="58"/>
                    <toolbarItem reference="Ky3-6Y-3U1"/>
                    <toolbarItem reference="59"/>
                    <toolbarItem reference="dJx-dw-gcC"/>
                    <toolbarItem reference="60"/>
                </defaultToolbarItems>
            </toolbar>
            <connections>
                <outlet property="delegate" destination="-2" id="4"/>
            </connections>
            <point key="canvasLocation" x="-136" y="158"/>
        </window>
    </objects>
    <resources>
        <image name="NSEnterFullScreenTemplate" width="15" height="15"/>
        <image name="NSGoLeftTemplate" width="9" height="12"/>
        <image name="NSGoRightTemplate" width="9" height="12"/>
        <image name="NSLockUnlockedTemplate" width="10" height="14"/>
        <image name="NSRefreshTemplate" width="11" height="15"/>
        <image name="NSShareTemplate" width="11" height="16"/>
    </resources>
</document>
