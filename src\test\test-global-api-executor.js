/**
 * 测试基于全局API的执行器
 * 
 * 这个测试需要在应用了新patch的环境中运行
 */

process.env = {
  ...process.env,
  PLAYWRIGHT_REPLAY_API_PATCH: '1'
};

const { GlobalApiExecutor } = require('../main/global-api-executor');

async function testGlobalApiExecutor() {
  console.log('🧪 开始测试基于全局API的执行器...');
  
  const executor = new GlobalApiExecutor({
    enableDiagnostics: true
  });

  try {
    // 检查全局API可用性
    console.log('\n📋 检查全局API状态:');
    const apiInfo = executor.getGlobalApiInfo();
    console.log('API信息:', apiInfo);
    
    if (!apiInfo.available) {
      console.log('❌ 全局API不可用，请确保：');
      console.log('1. 已应用新的patch (包含 PLAYWRIGHT_REPLAY_API_PATCH)');
      console.log('2. 启动了Playwright录制器或相关服务');
      console.log('3. 环境变量设置正确');
      return;
    }
    
    // 初始化执行器
    console.log('\n🚀 初始化执行器...');
    const config = {
      browserName: 'chromium',
      launchOptions: {
        headless: false
      },
      contextOptions: {
        viewport: { width: 1280, height: 720 }
      }
    };
    
    await executor.initialize(config);
    console.log('✅ 执行器初始化成功');
    
    // 测试基本动作
    console.log('\n🎬 测试基本动作...');
    
    // 导航到百度
    await executor.executeAction({
      name: 'navigate',
      url: 'https://www.baidu.com'
    });
    console.log('✅ 导航成功');
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 填写搜索框
    await executor.executeAction({
      name: 'fill',
      selector: '#kw',
      text: 'Playwright 测试'
    });
    console.log('✅ 填写搜索框成功');
    
    // 点击搜索按钮
    await executor.executeAction({
      name: 'click',
      selector: '#su'
    });
    console.log('✅ 点击搜索按钮成功');
    
    // 等待搜索结果
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎉 所有测试通过！');
    console.log('📊 测试总结:');
    console.log('- 全局API可用性: ✅');
    console.log('- 执行器初始化: ✅');
    console.log('- 导航动作: ✅');
    console.log('- 填写动作: ✅');
    console.log('- 点击动作: ✅');
    
    // 保持浏览器打开一段时间以便观察
    console.log('\n⏰ 保持浏览器打开10秒以便观察结果...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
  } finally {
    await executor.close();
    console.log('🔚 测试完成，浏览器已关闭');
  }
}

async function testGlobalApiDirectly() {
  console.log('\n🔬 直接测试全局API...');
  
  if (!global.playwrightReplayAPI) {
    console.log('❌ 全局API不可用');
    return;
  }
  
  console.log('✅ 全局API可用');
  console.log('📋 API版本:', global.playwrightReplayAPI.version);
  console.log('📋 Patch版本:', global.playwrightReplayAPI.patchVersion);
  console.log('📋 可用方法:', Object.keys(global.playwrightReplayAPI));
  
  // 测试工具函数
  try {
    const callMetadata = global.playwrightReplayAPI.serverSideCallMetadata();
    console.log('✅ serverSideCallMetadata 函数可用');
    console.log('📊 callMetadata 示例:', {
      type: callMetadata.type,
      isServerSide: callMetadata.isServerSide
    });
  } catch (error) {
    console.error('❌ serverSideCallMetadata 测试失败:', error.message);
  }
}

// 主测试函数
async function main() {
  console.log('🎭 Playwright 全局API执行器测试套件');
  console.log('=====================================');
  
  // 首先测试全局API是否可用
  await testGlobalApiDirectly();
  
  // 然后测试完整的执行器
  await testGlobalApiExecutor();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testGlobalApiExecutor,
  testGlobalApiDirectly,
  main
};
