name: Publish Test Results
on:
  workflow_run:
    workflows: ["tests 1", "tests 2", "tests others"]
    types:
      - completed
jobs:
  merge-reports:
    permissions:
      pull-requests: write
      checks: write
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    if: ${{ github.event.workflow_run.event == 'pull_request' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
      env:
        ELECTRON_SKIP_BINARY_DOWNLOAD: 1
    - run: npm run build

    - name: Download blob report artifact
      uses: ./.github/actions/download-artifact
      with:
        namePrefix: 'blob-report'
        path: 'all-blob-reports'

    - name: Merge reports
      run: |
        npx playwright merge-reports --config .github/workflows/merge.config.ts ./all-blob-reports
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        NODE_OPTIONS: --max-old-space-size=8192
        HTML_REPORT_URL: 'https://mspwblobreport.z1.web.core.windows.net/run-${{ github.event.workflow_run.id }}-${{ github.event.workflow_run.run_attempt }}-${{ github.sha }}/index.html'

    - name: Azure Login
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.AZURE_BLOB_REPORTS_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_BLOB_REPORTS_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_BLOB_REPORTS_SUBSCRIPTION_ID }}

    - name: Upload HTML report to Azure
      run: |
        REPORT_DIR='run-${{ github.event.workflow_run.id }}-${{ github.event.workflow_run.run_attempt }}-${{ github.sha }}'
        azcopy cp --recursive "./playwright-report/*" "https://mspwblobreport.blob.core.windows.net/\$web/$REPORT_DIR"
        echo "Report url: https://mspwblobreport.z1.web.core.windows.net/$REPORT_DIR/index.html"
      env:
        AZCOPY_AUTO_LOGIN_TYPE: AZCLI
