diff --git a/node_modules/playwright-core/lib/server/recorder.js b/node_modules/playwright-core/lib/server/recorder.js
index fd5a574..150eb82 100644
--- a/node_modules/playwright-core/lib/server/recorder.js
+++ b/node_modules/playwright-core/lib/server/recorder.js
@@ -306,7 +306,53 @@ class Recorder {
   }
   _pushAllSources() {
     const primaryPage = this._context.pages()[0];
-    this._recorderApp?.setSources([...this._recorderSources, ...this._userSources.values()], primaryPage?.mainFrame().url());
+    const sources = [...this._recorderSources, ...this._userSources.values()];
+    const primaryPageURL = primaryPage?.mainFrame().url();
+
+    // 原有逻辑：发送到工具栏
+    this._recorderApp?.setSources(sources, primaryPageURL);
+
+    // ELECTRON_BRIDGE_PATCH_APPLIED - Electron 集成补丁
+    if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+      try {
+        // 直接通过 Electron IPC 发送数据到主进程
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPageURL,
+          timestamp: Date.now(),
+          mode: this._mode,
+          recorderSources: this._recorderSources,
+          userSources: Array.from(this._userSources.values())
+        };
+
+        // 检查是否在 Electron 环境中
+        if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+          // 直接使用 Electron 的 IPC 通信
+          const { BrowserWindow } = require('electron');
+          const allWindows = BrowserWindow.getAllWindows();
+
+          // 发送到所有渲染进程
+          allWindows.forEach(window => {
+            if (!window.isDestroyed()) {
+              window.webContents.send('playwright-code-generated', electronData);
+            }
+          });
+
+          // 尝试直接调用全局的 Electron 录制器实例（如果存在）
+          if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+            global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+            console.log('📡 已直接调用 Electron 录制器处理 Playwright 代码生成');
+          } else {
+            console.log('📡 已通过 Electron IPC 发送代码生成数据到所有窗口');
+          }
+        } else {
+          console.log('⚠️ 不在 Electron 环境中，跳过 IPC 通信');
+        }
+      } catch (error) {
+        console.log('⚠️ Electron IPC 集成失败:', error.message);
+      }
+    }
+
+    // PLAYWRIGHT_REPLAY_API_PATCH - 暴露官方回放API到全局变量
+    if (!global.playwrightReplayAPI) {
+      try {
+        // 导入必要的模块
+        const { performAction } = require('./recorder/recorderRunner');
+        const { RecorderCollection } = require('./recorder/recorderCollection');
+        const { mainFrameForAction, buildFullSelector } = require('./recorder/recorderUtils');
+        const { serverSideCallMetadata } = require('./instrumentation');
+
+        // 创建全局API对象
+        global.playwrightReplayAPI = {
+          // 核心回放函数
+          performAction: performAction,
+
+          // RecorderCollection类
+          RecorderCollection: RecorderCollection,
+
+          // 工具函数
+          mainFrameForAction: mainFrameForAction,
+          buildFullSelector: buildFullSelector,
+          serverSideCallMetadata: serverSideCallMetadata,
+
+          // 便捷方法：创建RecorderCollection实例
+          createRecorderCollection: (pageAliases) => {
+            return new RecorderCollection(pageAliases);
+          },
+
+          // 便捷方法：执行单个动作
+          executeAction: async (pageAliases, actionInContext) => {
+            return await performAction(pageAliases, actionInContext);
+          },
+
+          // 获取当前上下文信息
+          getContext: () => {
+            return {
+              context: this._context,
+              pages: this._context.pages(),
+              primaryPage: this._context.pages()[0]
+            };
+          },
+
+          // 版本信息
+          version: '1.53.1',
+          patchVersion: '1.0.0'
+        };
+
+        console.log('🎭 Playwright 官方回放API已暴露到 global.playwrightReplayAPI');
+        console.log('📋 可用方法:', Object.keys(global.playwrightReplayAPI));
+
+      } catch (error) {
+        console.log('⚠️ 暴露 Playwright 回放API失败:', error.message);
+      }
+    }
   }
   async onBeforeInputAction(sdkObject, metadata) {
   }
