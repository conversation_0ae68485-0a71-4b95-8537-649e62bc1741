/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

.copy-icon {
  flex: none;
  height: 24px;
  width: 24px;
  border: none;
  outline: none;
  color: var(--color-fg-muted);
  background: transparent;
  padding: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.copy-icon svg {
  margin: 0;
}

.copy-icon:not(:disabled):hover {
  background-color: var(--color-border-default);
}

.copy-button-container {
  visibility: hidden;
  display: inline-flex;
  margin-left: 8px;
  vertical-align: bottom;
}

.copy-value-container:hover .copy-button-container {
  visibility: visible;
}
