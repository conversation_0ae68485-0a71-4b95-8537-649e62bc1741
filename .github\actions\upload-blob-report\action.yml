name: 'Upload blob report'
description: 'Upload blob report to GitHub artifacts (for pull requests)'
inputs:
  report_dir:
    description: 'Directory containing blob report'
    required: true
    default: 'test-results/blob-report'
  job_name:
    description: 'Unique job name'
    required: true
    default: ''
runs:
  using: "composite"
  steps:
    - name: Integrity check
      shell: bash
      run: find "${{ inputs.report_dir }}" -name "*.zip" -exec unzip -t {} \;
    - name: Upload blob report to GitHub
      if: ${{ !cancelled() && github.event_name == 'pull_request' }}
      uses: actions/upload-artifact@v4
      with:
        name: blob-report-${{ inputs.job_name }}
        path: ${{ inputs.report_dir }}/**
        retention-days: 7
