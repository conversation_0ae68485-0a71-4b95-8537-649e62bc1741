name: "components"

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    paths-ignore:
      - 'browser_patches/**'
      - 'docs/**'
    branches:
      - main
      - release-*

env:
  FORCE_COLOR: 1
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  test_components:
    name: ${{ matrix.os }} - Node.js ${{ matrix.node-version }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        node-version: [18]
        include:
          - os: ubuntu-latest
            node-version: 20
          - os: ubuntu-latest
            node-version: 22
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm ci
    - run: npm run build
    - run: npx playwright install --with-deps
    - run: npm run ct
