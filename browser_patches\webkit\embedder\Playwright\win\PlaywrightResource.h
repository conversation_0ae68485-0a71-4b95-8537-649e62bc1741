//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by PlaywrightLauncher.rc
//
#define IDD_PLAYWRIGHT_DIALOG          102
#define IDI_PLAYWRIGHT                 107
#define IDR_MAINFRAME                   128
#define IDC_STATIC                      -1

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        129
#define _APS_NEXT_COMMAND_VALUE         32771
#define _APS_NEXT_CONTROL_VALUE         1000
#define _APS_NEXT_SYMED_VALUE           110
#endif
#endif
