/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

.tree-item {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 38px;
}

.tree-item-title {
  cursor: pointer;
}

.tree-item-body {
  min-height: 18px;
}

.yellow-flash {
  animation: yellowflash-bg 2s;
}
@keyframes yellowflash-bg {
  from { background: var(--color-attention-subtle); }
  to   { background: transparent; }
}
