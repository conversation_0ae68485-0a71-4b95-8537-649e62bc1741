name: "publish release - driver"

on:
  release:
    types: [published]

env:
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  publish-driver-release:
    name: "publish playwright driver to CDN"
    runs-on: ubuntu-24.04
    if: github.repository == 'microsoft/playwright'
    permissions:
      id-token: write  # This is required for OIDC login (azure/login) to succeed
      contents: read   # This is required for actions/checkout to succeed
    environment: allow-publish-driver-to-cdn # This is required for OIDC login (azure/login)
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        registry-url: 'https://registry.npmjs.org'
    - run: npm ci
    - run: npm run build
    - run: utils/build/build-playwright-driver.sh
    - name: Azure Login
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.AZURE_PW_CDN_CLIENT_ID }}
        tenant-id: ${{ secrets.AZURE_PW_CDN_TENANT_ID }}
        subscription-id: ${{ secrets.AZURE_PW_CDN_SUBSCRIPTION_ID }}
    - run: utils/build/upload-playwright-driver.sh
      env:
        AZ_UPLOAD_FOLDER: driver
