name: "Internal Tests"

on:
  push:
    branches:
      - main
      - release-*

jobs:
  trigger:
    name: "trigger"
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/create-github-app-token@v2
      id: app-token
      with:
        app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
        private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
        repositories: playwright-browsers
    - run: |
        curl -X POST \
          -H "Accept: application/vnd.github.v3+json" \
          -H "Authorization: token ${GH_TOKEN}" \
          --data "{\"event_type\": \"playwright_tests\", \"client_payload\": {\"ref\": \"${GITHUB_SHA}\"}}" \
          https://api.github.com/repos/microsoft/playwright-browsers/dispatches
      env:
        GH_TOKEN: ${{ steps.app-token.outputs.token }}
