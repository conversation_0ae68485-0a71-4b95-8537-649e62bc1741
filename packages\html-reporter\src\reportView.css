/*
  Copyright (c) Microsoft Corporation.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/

html, body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overscroll-behavior-x: none;
}

body {
  overflow: auto;
  max-width: 1024px;
  margin: 0 auto;
  width: 100%;
}

.test-file-test:not(:first-child) {
  border-top: 1px solid var(--color-border-default);
}

@media only screen and (max-width: 600px) {
  .htmlreport {
    padding: 0 !important;
  }
}
