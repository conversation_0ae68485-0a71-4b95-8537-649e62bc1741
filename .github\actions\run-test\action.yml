name: 'Run browser tests'
description: 'Run browser tests'
inputs:
  command:
    description: 'Command to run tests'
    required: true
  node-version:
    description: 'Node.js version to use'
    required: false
    default: '18'
  browsers-to-install:
    description: 'Browser to install. Default is all browsers.'
    required: false
    default: ''
  bot-name:
    description: 'Bot name'
    required: true
  shell:
    description: 'Shell to use'
    required: false
    default: 'bash'
  flakiness-client-id:
    description: 'Azure Flakiness Dashboard Client ID'
    required: false
  flakiness-tenant-id:
    description: 'Azure Flakiness Dashboard Tenant ID'
    required: false
  flakiness-subscription-id:
    description: 'Azure Flakiness Dashboard Subscription ID'
    required: false

runs:
  using: composite
  steps:
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
    - uses: ./.github/actions/enable-microphone-access
    - run: |
        echo "::group::npm ci"
        npm ci
        echo "::endgroup::"
      shell: bash
    - run: |
        echo "::group::npm run build"
        npm run build
        echo "::endgroup::"
      shell: bash
    - run: |
        echo "::group::npx playwright install --with-deps"
        npx playwright install --with-deps ${{ inputs.browsers-to-install }}
        echo "::endgroup::"
      shell: bash
    - name: Run tests
      if: inputs.shell == 'bash'
      run: |
        if [[ "$(uname)" == "Linux" ]]; then
          xvfb-run --auto-servernum --server-args="-screen 0 1280x960x24" -- ${{ inputs.command }}
        else
          ${{ inputs.command }}
        fi
      shell: bash
      env:
        PWTEST_BOT_NAME: ${{ inputs.bot-name }}
    - name: Run tests
      if: inputs.shell != 'bash'
      run: ${{ inputs.command }}
      shell: ${{ inputs.shell }}
      env:
        PWTEST_BOT_NAME: ${{ inputs.bot-name }}
    - name: Azure Login
      uses: azure/login@v2
      if: ${{ !cancelled() && github.event_name == 'push' && github.repository == 'microsoft/playwright' }}
      with:
        client-id: ${{ inputs.flakiness-client-id }}
        tenant-id: ${{ inputs.flakiness-tenant-id }}
        subscription-id: ${{ inputs.flakiness-subscription-id }}
    - run: |
        echo "::group::./utils/upload_flakiness_dashboard.sh"
        ./utils/upload_flakiness_dashboard.sh ./test-results/report.json
        echo "::endgroup::"
      if: ${{ !cancelled() }}
      shell: bash
    - name: Upload blob report
      # We only merge reports for PRs as per .github/workflows/create_test_report.yml.
      if: ${{ !cancelled() && github.event_name == 'pull_request' }}
      uses: ./.github/actions/upload-blob-report
      with:
        report_dir: blob-report
        job_name: ${{ inputs.bot-name }}
