name: "infra"

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    branches:
      - main
      - release-*

env:
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  doc-and-lint:
    name: "docs & lint"
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - run: npm ci
    - run: npm run build
    - run: npx playwright install --with-deps
    - run: npm run lint
    - name: Verify clean tree
      run: |
        if [[ -n $(git status -s) ]]; then
          echo "ERROR: tree is dirty after npm run build:"
          git diff
          exit 1
        fi
    - name: Audit prod NPM dependencies
      run: node utils/check_audit.js
      continue-on-error: true
  lint-snippets:
    name: "Lint snippets"
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
    - uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    - uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x
    - uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '21'
    - run: npm ci
    - run: pip install -r utils/doclint/linting-code-snippets/python/requirements.txt
    - run: mvn package
      working-directory: utils/doclint/linting-code-snippets/java
    - run: node utils/doclint/linting-code-snippets/cli.js
