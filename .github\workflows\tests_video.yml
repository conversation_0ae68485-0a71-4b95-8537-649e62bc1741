name: "tests Video"

on:
  push:
    branches:
      - main
      - release-*

env:
  # Force terminal colors. @see https://www.npmjs.com/package/colors
  FORCE_COLOR: 1
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  video_linux:
    name: "Video Linux"
    environment: allow-uploading-flakiness-results
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        os: [ubuntu-22.04, ubuntu-24.04]
    permissions:
      id-token: write   # This is required for OIDC login (azure/login) to succeed
      contents: read    # This is required for actions/checkout to succeed
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/run-test
      with:
        browsers-to-install: ${{ matrix.browser }} chromium
        command: npm run test -- --project=${{ matrix.browser }}-*
        bot-name: "${{ matrix.browser }}-${{ matrix.os }}"
        flakiness-client-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_CLIENT_ID }}
        flakiness-tenant-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_TENANT_ID }}
        flakiness-subscription-id: ${{ secrets.AZURE_FLAKINESS_DASHBOARD_SUBSCRIPTION_ID }}
      env:
        PWTEST_VIDEO: 1
