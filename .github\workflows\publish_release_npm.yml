name: "publish release - NPM"

on:
  release:
    types: [published]

env:
  ELECTRON_SKIP_BINARY_DOWNLOAD: 1

jobs:
  publish-npm-release:
    name: "publish to N<PERSON>"
    runs-on: ubuntu-24.04
    if: github.repository == 'microsoft/playwright'
    permissions:
      contents: read
      id-token: write
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        registry-url: 'https://registry.npmjs.org'
    - run: npm ci
    - run: npm run build
    - run: utils/publish_all_packages.sh --release-candidate
      if: ${{ github.event.release.prerelease }}
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
    - run: utils/publish_all_packages.sh --release
      if: ${{ !github.event.release.prerelease }}
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
