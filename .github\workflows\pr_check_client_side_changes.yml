name: "Check client side changes"
on:
  push:
    branches:
      - main
    paths:
      - 'docs/src/api/**/*'
      - 'packages/playwright-core/src/client/**/*'
      - 'packages/playwright-core/src/utils/isomorphic/**/*'
      - 'packages/playwright/src/matchers/matchers.ts'
      - 'packages/protocol/src/protocol.yml'
jobs:
  check:
    name: Check
    runs-on: ubuntu-24.04
    if: github.repository == 'microsoft/playwright'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/create-github-app-token@v2
        id: app-token
        with:
          app-id: ${{ vars.PLAYWRIGHT_APP_ID }}
          private-key: ${{ secrets.PLAYWRIGHT_PRIVATE_KEY }}
          repositories: |
            playwright
            playwright-python
            playwright-java
            playwright-dotnet
      - name: Create GitHub issue
        uses: actions/github-script@v7
        with:
          github-token: ${{ steps.app-token.outputs.token }}
          script: |
            const currentPlaywrightVersion = require('./package.json').version.match(/\d+\.\d+/)[0];
            const { data } = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha,
            });
            const commitHeader = data.message.split('\n')[0];
            const prMatch = commitHeader.match(/#(\d+)/);
            const formattedCommit = prMatch 
              ? `https://github.com/microsoft/playwright/pull/${prMatch[1]}`
              : `https://github.com/${context.repo.owner}/${context.repo.repo}/commit/${context.sha} (${commitHeader})`;

            const title = '[Ports]: Backport client side changes for ' + currentPlaywrightVersion;
            for (const repo of ['playwright-python', 'playwright-java', 'playwright-dotnet']) {
              const { data: issuesData } = await github.rest.search.issuesAndPullRequests({
                q: `is:issue is:open repo:microsoft/${repo} in:title "${title}"`
              })
              let issueNumber = null;
              let issueBody = '';
              if (issuesData.total_count > 0) {
                issueNumber = issuesData.items[0].number
                issueBody = issuesData.items[0].body
              } else {
                const { data: issueCreateData } = await github.rest.issues.create({
                  owner: context.repo.owner,
                  repo: repo,
                  title,
                  body: 'Please backport client side changes: \n',
                });
                issueNumber = issueCreateData.number;
                issueBody = issueCreateData.body;
              }
              const newBody = issueBody.trimEnd() + `
              - [ ] ${formattedCommit}`;
              const data = await github.rest.issues.update({
                owner: context.repo.owner,
                repo: repo,
                issue_number: issueNumber,
                body: newBody
              })
            }
